package lk.sout.core.service.impl;

import lk.sout.core.entity.Module;
import lk.sout.core.entity.Permission;
import lk.sout.core.entity.Response;
import lk.sout.core.entity.User;
import lk.sout.core.entity.UserRole;
import lk.sout.core.entity.RoleName;
import lk.sout.core.repository.ModuleRepository;
import lk.sout.core.repository.PermissionRepository;
import lk.sout.core.repository.UserRepository;
import lk.sout.core.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Service(value = "userService")
public class UserServiceImpl implements UserDetailsService, UserService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ModuleRepository moduleRepository;

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private BCryptPasswordEncoder passwordEncoder;

    @Autowired
    Response response;

    public UserDetails loadUserByUsername(String userId) throws UsernameNotFoundException {
        User user = userRepository.findByUsername(userId);
        if (user == null) {
            throw new UsernameNotFoundException("Invalid username or password.");
        }
        return new org.springframework.security.core.userdetails.User(user.getUsername(), user.getPassword(), getAuthority());
    }

    private List<SimpleGrantedAuthority> getAuthority() {
        return Arrays.asList(new SimpleGrantedAuthority("ROLE_ADMIN"));
    }

    public List<User> findAll() {
        List<User> list = new ArrayList<>();
        userRepository.findAll().iterator().forEachRemaining(list::add);
        return list;
    }

    @Override
    public User getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String userName = authentication.getName();
            User user = userRepository.findByUsername(userName);
            return user;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public boolean isAdmin() {
        if (getCurrentUser().getUserRoles().stream().anyMatch(a ->
                a.getName().toString().equals("ADMIN"))) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean isManager() {
        if (getCurrentUser().getUserRoles().stream().anyMatch(a ->
                a.getName().toString().equals("MANAGER"))) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public String delete(String id) {
        try {
            userRepository.deleteById(id);
            return "success";
        } catch (Exception ex) {
            LOGGER.error("Removing user failed " + ex.getMessage());
            return "failed";
        }
    }

    @Override
    public User search(String username) {
        try {
            return userRepository.findByUsername(username);
        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public List<Permission> findAvailablePermissions(String userName) {
        try {
            return userRepository.findByUsername(userName).getPermissions();
        } catch (Exception ex) {
            LOGGER.error("Finding enabled permissions failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Permission> findDesktopPermissions(String userName) {
        try {
            List<Permission> permissions = userRepository.findByUsername(userName).getDesktopPermissions();
            return permissions;
        } catch (Exception ex) {
            LOGGER.error("Finding desktop permissions failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Permission> findPermissionsByModule(String moduleId) {
        try {
            List<Permission> permissions = permissionRepository.findByModule(moduleRepository.findById(moduleId).get());
            return permissions;
        } catch (Exception ex) {
            LOGGER.error("Find Permissions by Module " + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean saveDesktopPerms(String username, List<Permission> permissions) {
        try {
            User user = userRepository.findByUsername(username);
            user.setDesktopPermissions(permissions);
            userRepository.save(user);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Add Desktop Permission failed " + ex.getMessage());
            return false;
        }
    }

    @Override
    public List<Module> getEnabledModules() {
        try {
            List<Module> modules = moduleRepository.findAllByActivated(true);
            return modules;
        } catch (Exception ex) {
            LOGGER.error("Finding enabled modules failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Module findModule(String moduleId) {
        return moduleRepository.findById(moduleId).get();
    }

    @Override
    public Permission findPermission(String permissionId) {
        return permissionRepository.findById(permissionId).get();
    }

    @Override
    public Boolean checkName(String username) {
        try {
            if (null != userRepository.findByUsername(username)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            LOGGER.error("check Customer by nic Failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Response update(User user) {
        try {
            userRepository.save(user);
            response.setCode(200);
            response.setMessage("Desktop permission added successfully");
        } catch (Exception e) {
            response.setMessage("Add desktop permission failed");
        }
        return response;
    }

    @Override
    public User searchByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    @Override
    public Permission findPermissionsByName(String permName) {
        return permissionRepository.findByName(permName);
    }

    @Override
    public User findUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String userName = authentication.getName();

            User user = userRepository.findByUsername(userName);
            return user;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public List<User> findUsersWithCashierRole() {
        try {
            // Get all users
            List<User> allUsers = findAll();

            // Filter active users who have the CASHIER role
            List<User> cashierUsers = allUsers.stream()
                .filter(user -> user.isActive() && user.getUserRoles() != null &&
                        user.getUserRoles().stream()
                            .anyMatch(role -> role.getName() != null &&
                                    role.getName().toString().equals(RoleName.CASHIER.toString())))
                .collect(Collectors.toList());

            LOGGER.info("Found " + cashierUsers.size() + " users with CASHIER role");
            return cashierUsers;
        } catch (Exception ex) {
            LOGGER.error("Error finding users with CASHIER role: " + ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    @Override
    public User findOne(String username) {
        return userRepository.findByUsername(username);
    }

    @Override
    public Optional<User> findById(String id) {
        return userRepository.findById(id);
    }

    @Override
    public boolean save(User user) {
        try {
            if (user.getId() == null || !user.getPassword().equals("NOCHNG")) {
                user.setPassword(passwordEncoder.encode(user.getPassword()));
            }
            if (user.getId() != null && user.getPassword().equals("NOCHNG")) {
                user.setPassword(userRepository.findById(user.getId()).get().getPassword());
            }
            userRepository.save(user);
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            LOGGER.error("Add user failed " + ex.getMessage());
            return false;
        }
    }
}
