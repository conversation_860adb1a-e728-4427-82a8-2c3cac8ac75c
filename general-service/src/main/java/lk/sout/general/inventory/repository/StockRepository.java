/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package lk.sout.general.inventory.repository;

import lk.sout.general.inventory.entity.Stock;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface StockRepository extends MongoRepository<Stock, String> {

    List<Stock> findByBarcodeAndWarehouseCode(String barcode, int warehouseCode);

    @Query(fields = "{'barcode':1,'quantity':1,'itemName':1,'itemCode':1,'sellingPrice':1, 'itemCost':1}")
    List<Stock> findTop15ByWarehouseCodeAndBarcodeLikeIgnoreCase(int no, String barcode);

    @Query(fields = "{'barcode':1,'quantity':1,'itemName':1,'itemCode':1,'sellingPrice':1, 'itemCost':1}")
    List<Stock> findTop15ByWarehouseCodeAndItemNameLikeIgnoreCase(int no, String itemName);

    Stock findByItemCodeAndWarehouseCodeAndSellingPrice(String itemCode, int no, double price);

    List<Stock> findByItemCode(String itemCode);

    //  @Query(fields = "{'barcode':1,'quantity':1,'sellingPrice':1,'itemCost':1,'warehouseName':1}")
    Page<Stock> findAllByWarehouseCode(Pageable pageable, int no);

    List<Stock> findAllByBarcodeLikeIgnoreCase(String barcode);

    List<Stock> findAllByItemNameLikeIgnoreCase(String name);

    List<Stock> findAllByCategoryCodeAndWarehouseCode(String code, int whCode);

    List<Stock> findAllByBrandCodeAndWarehouseCode(String code, int whCode);

}
