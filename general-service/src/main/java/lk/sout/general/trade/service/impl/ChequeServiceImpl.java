package lk.sout.general.trade.service.impl;

import lk.sout.general.trade.entity.Cheque;
import lk.sout.general.trade.entity.PurchaseInvoice;
import lk.sout.general.trade.entity.SalesInvoice;
import lk.sout.general.trade.repository.ChequeRepository;
import lk.sout.general.trade.service.ChequeService;
import lk.sout.general.trade.service.PurchaseInvoiceService;
import lk.sout.general.trade.service.SalesInvoiceService;
import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.core.entity.Transaction;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.TransactionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class ChequeServiceImpl implements ChequeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ChequeServiceImpl.class);

    @Autowired
    ChequeRepository chequeRepository;

    @Autowired
    MetaData status;

    @Autowired
    TransactionService transactionService;

    @Autowired
    SalesInvoiceService salesInvoiceService;

    @Autowired
    Transaction transaction;

    @Autowired
    Response response;

    @Autowired
    SalesInvoice salesInvoice;

    @Autowired
    PurchaseInvoice purchaseInvoice;

    @Autowired
    PurchaseInvoiceService purchaseInvoiceService;

    @Autowired
    MetaDataService metaDataService;

    @Override
    public Cheque basicSave(Cheque cheque) {
        try {
            return chequeRepository.save(cheque);
        } catch (Exception e) {
            LOGGER.error("Saving Cheque Failed " + e.getStackTrace()[0].getLineNumber() + " : " +
                    e.getMessage());
            return null;
        }
    }

    @Override
    public Response save(Cheque cheque) {
        try {
            if (cheque.getId() != null) {
                checkPaymentUpdate(cheque);
            } else {
                // Set default cheque type if not specified
                if (cheque.getChequeType() == null || cheque.getChequeType().isEmpty()) {
                    // Default to RECEIVED for backward compatibility
                    cheque.setChequeType("RECEIVED");
                }

                status = metaDataService.searchMetaData("Pending", "ChequeStatus");
                cheque.setStatus(status);
                Cheque cheque1 = chequeRepository.save(cheque);
                if (cheque1.getId() != null) {
                    response.setData(cheque.getChequeNo());
                    response.setCode(200);
                    response.setMessage("Cheque Payment Created Successfully");
                }
            }

        } catch (Exception e) {
            LOGGER.error("Creating Cheque Payment Failed on line " + e.getStackTrace()[0].getLineNumber() + " : " +
                    e.getMessage());
            response.setCode(501);
            response.setMessage("Creating Cheque Payment Failed");
            response.setData(e.getMessage());
        }
        return response;
    }

    @Override
    @Transactional
    public Response updateCheque(String id, String comment, boolean isDeposit) {
        try {
            Optional<Cheque> optionalCheque = chequeRepository.findById(id);
            if (optionalCheque.isPresent()) {
                Cheque cheque = optionalCheque.get();
                MetaData depositStatus = metaDataService.searchMetaData("Deposited", "ChequeStatus");
                MetaData returnStatus = metaDataService.searchMetaData("Returned", "ChequeStatus");
                if (isDeposit) {
                    cheque.setStatus(depositStatus);
                    cheque.setComment(comment);
                    if (checkPaymentUpdate(cheque)) {
                        chequeRepository.save(cheque);
                        response.setCode(200);
                        response.setMessage("Cheque Deposited");
                    } else {
                        response.setCode(401);
                        response.setMessage("Depositing Cheque Failed");
                    }
                } else {
                    cheque.setStatus(returnStatus);
                    cheque.setComment(comment);
                    chequeRepository.save(cheque);
                    response.setCode(200);
                    response.setMessage("Cheque Returned");
                }
            }
            return response;
        } catch (Exception e) {
            response.setCode(500);
            response.setMessage("Cheque Updating Failed");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }
    }

    @Override
    @Transactional
    public boolean checkPaymentUpdate(Cheque cheque) {
        try {
            // Handle different types of cheques
            if ("RECEIVED".equals(cheque.getChequeType())) {
                // This is a customer cheque (received)
                return handleReceivedCheque(cheque);
            } else if ("GIVEN".equals(cheque.getChequeType())) {
                // This is a supplier cheque (given)
                return handleGivenCheque(cheque);
            } else {
                // Default to received cheque for backward compatibility
                return handleReceivedCheque(cheque);
            }
        } catch (Exception ex) {
            LOGGER.error("Updating by Cheque failed " + ex.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
    }

    @Override
    @Transactional
    public boolean handleReceivedCheque(Cheque cheque) {
        try {
            MetaData siType = metaDataService.searchMetaData("SalesInvoice", "Income");

            salesInvoice = salesInvoiceService.findByInvNo(cheque.getInvoiceNo());

            if (null != salesInvoice) {
                double payment = cheque.getChequeAmount() + salesInvoice.getPayment();

                if (payment >= salesInvoice.getTotalAmount()) {
                    salesInvoice.setPayment(salesInvoice.getTotalAmount());
                    salesInvoice.setBalance(Double.valueOf(0));
                    salesInvoice.setStatus(metaDataService.searchMetaData("Paid", "PaymentStatus"));
                } else {
                    salesInvoice.setPayment(payment);
                    double balance = payment - salesInvoice.getTotalAmount();
                    salesInvoice.setBalance(balance * (-1));
                    salesInvoice.setStatus(metaDataService.searchMetaData("Partially Paid", "PaymentStatus"));
                }

                salesInvoiceService.basicSave(salesInvoice);

                // Create transaction using the centralized method
                transactionService.createTransaction(
                    cheque.getChequeAmount(),           // amount
                    "+",                              // operator (income is positive)
                    salesInvoice.getInvoiceNo(),        // refNo
                    "Cheque Payment",                  // refType
                    salesInvoice.getCustomerName(),     // thirdParty
                    "Income",                         // typeCategory
                    "SalesInvoice",                   // typeValue
                    "Cheque",                         // paymentMethod
                    null,                             // remark
                    LocalDateTime.now()                // date
                );

                return true;
            }
            return false;
        } catch (Exception ex) {
            LOGGER.error("Updating Sales Invoice by Cheque failed " + ex.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
    }

    @Override
    @Transactional
    public boolean handleGivenCheque(Cheque cheque) {
        try {
            MetaData piType = metaDataService.searchMetaData("PurchaseInvoice", "Expense");

            PurchaseInvoice purchaseInvoice = purchaseInvoiceService.findByPurchaseInvoiceNo(cheque.getPurchaseInvoiceNo());

            if (null != purchaseInvoice) {
                double payment = cheque.getChequeAmount() + purchaseInvoice.getPayment();

                if (payment >= purchaseInvoice.getTotalAmount()) {
                    purchaseInvoice.setPayment(purchaseInvoice.getTotalAmount());
                    purchaseInvoice.setBalance(Double.valueOf(0));
                    purchaseInvoice.setStatus(metaDataService.searchMetaData("Paid", "PaymentStatus"));
                } else {
                    purchaseInvoice.setPayment(payment);
                    double balance = payment - purchaseInvoice.getTotalAmount();
                    purchaseInvoice.setBalance(balance * (-1));
                    purchaseInvoice.setStatus(metaDataService.searchMetaData("Partially Paid", "PaymentStatus"));
                }

                purchaseInvoiceService.basicSave(purchaseInvoice);

                // Create transaction using the centralized method
                transactionService.createTransaction(
                    cheque.getChequeAmount(),           // amount
                    "-",                              // operator (expense is negative)
                    purchaseInvoice.getInvoiceNo(),     // refNo
                    "Cheque Payment",                  // refType
                    purchaseInvoice.getSupplier().getName(), // thirdParty
                    "Expense",                        // typeCategory
                    "PurchaseInvoice",                // typeValue
                    "Cheque",                         // paymentMethod
                    null,                             // remark
                    LocalDateTime.now()                // date
                );

                return true;
            }
            return false;
        } catch (Exception ex) {
            LOGGER.error("Updating Purchase Invoice by Cheque failed " + ex.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
    }

    @Override
    public void update(String salesInvoiceId, String refType) {
        Cheque cheque1 = chequeRepository.findTopByOrderByIdDesc().get(0);
        chequeRepository.save(cheque1);
    }

    @Override
    public List<Cheque> findAll() {
        try {
            return chequeRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("Find All Cheque Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Cheque findById(String id) {
        return chequeRepository.findById(id).get();
    }

    @Override
    public Cheque findAllByChequeNo(String chequeNo) {
        return chequeRepository.findAllByChequeNoLike(chequeNo);
    }

    @Override
    public List<Cheque> findAllByDate(LocalDate date) {
        try {
            return chequeRepository.findAllByChequeDate(date);
        } catch (Exception e) {
            LOGGER.error("Find All Cheque Failed " + e.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Cheque> findAllPending(Pageable pageable) {
        try {
            MetaData status = metaDataService.searchMetaData("Pending", "ChequeStatus");
            Iterable<Cheque> cheques = chequeRepository.findAllByStatusId(status.getId(), pageable);
            return cheques;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Cheque> findAllByStatus(String chequeStatusId) {
        try {
            List<Cheque> chequeList = chequeRepository.findAllByStatusId(chequeStatusId);
            return chequeList;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Cheque> findAllByBank(String bankId) {
        try {
            List<Cheque> chequeList = chequeRepository.findAllByBankId(bankId);
            return chequeList;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Cheque> findAllByCustomer(String customerId) {
        try {
            List<Cheque> chequeList = chequeRepository.findAllByCustomerId(customerId);
            return chequeList;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Cheque> findAllBySupplier(String supplierId) {
        try {
            List<Cheque> chequeList = chequeRepository.findAllBySupplierId(supplierId);
            return chequeList;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Cheque> findAllByChequeType(String chequeType) {
        try {
            List<Cheque> chequeList = chequeRepository.findAllByChequeType(chequeType);
            return chequeList;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Cheque> findAllByPurchaseInvoiceNo(String purchaseInvoiceNo) {
        try {
            List<Cheque> chequeList = chequeRepository.findAllByPurchaseInvoiceNo(purchaseInvoiceNo);
            return chequeList;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public Integer loadAvailableChequeQty() {
        try {
            MetaData pendingStatus = metaDataService.searchMetaData("Pending", "ChequeStatus");
            List<Cheque> chequeList = chequeRepository.findAllByChequeDateBetweenAndStatus(LocalDate.now().atStartOfDay(), LocalDate.now().plusDays(1), pendingStatus.getId());
            return chequeList.size();
        } catch (Exception e) {
            return null;
        }
    }


}
