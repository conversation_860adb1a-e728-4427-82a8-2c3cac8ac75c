package lk.sout.general.tenant;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Component;

/**
 * Resolves tenant from various sources (header, subdomain, etc.)
 */
@Component
public class TenantResolver {
    
    private static final String TENANT_HEADER = "X-Tenant-ID";
    private static final String DEFAULT_TENANT = "default";
    
    public String resolveTenant(HttpServletRequest request) {
        // First try to get tenant from header
        String tenantFromHeader = request.getHeader(TENANT_HEADER);
        if (tenantFromHeader != null && !tenantFromHeader.trim().isEmpty()) {
            return tenantFromHeader.trim().toLowerCase();
        }
        
        // Try to extract from subdomain
        String host = request.getHeader("Host");
        if (host != null) {
            String tenantFromSubdomain = extractTenantFromSubdomain(host);
            if (tenantFromSubdomain != null) {
                return tenantFromSubdomain;
            }
        }
        
        // Try to extract from Origin header (for CORS requests)
        String origin = request.getHeader("Origin");
        if (origin != null) {
            String tenantFromOrigin = extractTenantFromOrigin(origin);
            if (tenantFromOrigin != null) {
                return tenantFromOrigin;
            }
        }
        
        return DEFAULT_TENANT;
    }
    
    private String extractTenantFromSubdomain(String host) {
        // Extract tenant from subdomain pattern: {tenant}.vaganana.com or {tenant}.viganana.com
        if (host.contains(".vaganana.com") || host.contains(".viganana.com")) {
            String[] parts = host.split("\\.");
            if (parts.length >= 3) {
                String subdomain = parts[0];
                // Skip common subdomains
                if (!subdomain.equals("www") && !subdomain.equals("api") && !subdomain.equals("service")) {
                    return subdomain.toLowerCase();
                }
            }
        }
        return null;
    }
    
    private String extractTenantFromOrigin(String origin) {
        // Extract tenant from origin URL
        if (origin.contains(".vaganana.com") || origin.contains(".viganana.com")) {
            String host = origin.replace("https://", "").replace("http://", "");
            return extractTenantFromSubdomain(host);
        }
        return null;
    }
}
