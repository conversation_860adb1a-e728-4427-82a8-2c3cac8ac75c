package lk.sout.general.tenant.repository;

import lk.sout.general.tenant.entity.Tenant;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TenantRepository extends MongoRepository<Tenant, String> {
    
    /**
     * Find tenant by tenant ID
     */
    Optional<Tenant> findByTenantId(String tenantId);
    
    /**
     * Find tenant by subdomain
     */
    Optional<Tenant> findBySubdomain(String subdomain);
    
    /**
     * Find all active tenants
     */
    List<Tenant> findByActiveTrue();
    
    /**
     * Find tenants by subscription plan
     */
    List<Tenant> findBySubscriptionPlan(String subscriptionPlan);
    
    /**
     * Check if tenant exists by tenant ID
     */
    boolean existsByTenantId(String tenantId);
    
    /**
     * Check if tenant exists by subdomain
     */
    boolean existsBySubdomain(String subdomain);
}
