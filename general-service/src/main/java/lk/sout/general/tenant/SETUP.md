# Quick Setup Guide for Your Multi-Tenant System

## Current Setup
- **demo.vaganana.com** → **generalWebDemo** database
- **wanigarathna.vaganana.com** → **generalWebWanigarathna** database

## How It Works

1. **Request comes to backend** from `demo.vaganana.com`
2. **TenantResolver** extracts `demo` from subdomain
3. **TenantContext** is set to `demo`
4. **TenantMongoTemplateProvider** routes to `generalWebDemo` database
5. **All operations** automatically use the correct database

## Testing the Setup

### 1. Test Tenant Resolution

```bash
# Test demo tenant
curl -H "Host: demo.vaganana.com" http://your-backend/tenant-test/info

# Test wanigarathna tenant  
curl -H "Host: wanigarathna.vaganana.com" http://your-backend/tenant-test/info
```

Expected response:
```json
{
  "currentTenantId": "demo",
  "tenantName": "Demo Tenant",
  "databaseName": "generalWebDemo",
  "subdomain": "demo",
  "active": true,
  "connectedDatabase": "generalWebDemo",
  "databaseConnectionStatus": "OK"
}
```

### 2. Test Database Operations

```bash
# Test database access for demo
curl -H "Host: demo.vaganana.com" http://your-backend/tenant-test/test-db

# Test database access for wanigarathna
curl -H "Host: wanigarathna.vaganana.com" http://your-backend/tenant-test/test-db
```

### 3. View All Configured Tenants

```bash
curl http://your-backend/tenant-test/all-tenants
```

## What Happens Automatically

1. **On Application Startup:**
   - `TenantDataInitializer` creates tenant configurations
   - Demo tenant → `generalWebDemo` database
   - Wanigarathna tenant → `generalWebWanigarathna` database

2. **On Each Request:**
   - `TenantInterceptor` extracts tenant from subdomain
   - `TenantContext` is set for the request
   - Database operations route to correct database
   - Context is cleared after request

3. **For Your Entities:**
   - All entities extending `TenantAware` automatically use tenant database
   - No code changes needed in your existing entities
   - Repositories automatically use correct database

## Migration Steps

### 1. Update Your Entities (Optional)
If you want to use the new system, make your entities extend `TenantAware`:

```java
// Before
@Document
public class Item {
    // your fields
}

// After  
@Document
public class Item extends TenantAware {
    // your fields - no changes needed
}
```

### 2. Update Your Repositories (Optional)
For new repositories, extend `TenantAwareRepository`:

```java
@Repository
public class ItemRepository extends TenantAwareRepository<Item> {
    public ItemRepository() {
        super(Item.class);
    }
    
    // Your custom methods automatically use tenant database
}
```

### 3. Keep Existing Code
Your existing repositories and services will continue to work as before. The multi-tenant system is additive.

## Verification Checklist

- [ ] Backend starts without errors
- [ ] Tenant configurations are created automatically
- [ ] `demo.vaganana.com` requests use `generalWebDemo` database
- [ ] `wanigarathna.vaganana.com` requests use `generalWebWanigarathna` database
- [ ] Test endpoints return correct tenant information
- [ ] Database operations work correctly for both tenants

## Troubleshooting

### Issue: Tenant not found
- Check if subdomain matches exactly (`demo`, `wanigarathna`)
- Verify tenant was created in startup logs

### Issue: Database connection failed
- Check MongoDB credentials in `application.properties`
- Verify databases `generalWebDemo` and `generalWebWanigarathna` exist
- Test connection manually

### Issue: Wrong database being used
- Check tenant resolution in test endpoint
- Verify `TenantContext` is set correctly
- Check logs for tenant switching

## Next Steps

1. **Test the current setup** with existing frontends
2. **Verify data isolation** between tenants
3. **Add new tenants** as needed using the tenant API
4. **Monitor performance** and connection usage

The system is designed to work with your existing setup with minimal changes!
