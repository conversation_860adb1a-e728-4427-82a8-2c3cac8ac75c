package lk.sout.general.tenant.service;

import lk.sout.general.tenant.entity.Tenant;
import java.util.List;
import java.util.Optional;

public interface TenantService {
    
    /**
     * Create a new tenant
     */
    Tenant createTenant(Tenant tenant);
    
    /**
     * Get tenant by ID
     */
    Optional<Tenant> getTenantById(String tenantId);
    
    /**
     * Get tenant by subdomain
     */
    Optional<Tenant> getTenantBySubdomain(String subdomain);
    
    /**
     * Get all active tenants
     */
    List<Tenant> getAllActiveTenants();
    
    /**
     * Update tenant
     */
    Tenant updateTenant(Tenant tenant);
    
    /**
     * Activate/Deactivate tenant
     */
    void setTenantActive(String tenantId, boolean active);
    
    /**
     * Check if tenant is valid and active
     */
    boolean isValidTenant(String tenantId);
    
    /**
     * Delete tenant (soft delete)
     */
    void deleteTenant(String tenantId);
    
    /**
     * Get current tenant from context
     */
    String getCurrentTenantId();
    
    /**
     * Get current tenant entity
     */
    Optional<Tenant> getCurrentTenant();

    /**
     * Test database connection for tenant
     */
    boolean testTenantDatabaseConnection(String tenantId);

    /**
     * Refresh tenant database connection
     */
    void refreshTenantDatabaseConnection(String tenantId);
}
