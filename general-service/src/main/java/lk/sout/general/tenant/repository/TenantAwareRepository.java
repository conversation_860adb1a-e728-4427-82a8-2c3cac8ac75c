package lk.sout.general.tenant.repository;

import lk.sout.general.tenant.database.TenantMongoTemplateProvider;
import lk.sout.general.tenant.entity.TenantAware;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.repository.support.PageableExecutionUtils;

import java.util.List;
import java.util.Optional;

/**
 * Base repository for tenant-aware entities
 * Uses tenant-specific databases for complete data isolation
 */
public abstract class TenantAwareRepository<T extends TenantAware> {

    @Autowired
    protected TenantMongoTemplateProvider templateProvider;

    protected final Class<T> entityClass;

    public TenantAwareRepository(Class<T> entityClass) {
        this.entityClass = entityClass;
    }

    /**
     * Get MongoTemplate for current tenant
     */
    protected MongoTemplate getMongoTemplate() {
        return templateProvider.getCurrentTenantTemplate();
    }
    
    /**
     * Find all entities (from tenant-specific database)
     */
    public List<T> findAll() {
        return getMongoTemplate().findAll(entityClass);
    }
    
    /**
     * Find all entities with pagination (from tenant-specific database)
     */
    public Page<T> findAll(Pageable pageable) {
        Query query = new Query().with(pageable);
        List<T> content = getMongoTemplate().find(query, entityClass);

        return PageableExecutionUtils.getPage(content, pageable, () ->
            getMongoTemplate().count(new Query(), entityClass));
    }
    
    /**
     * Find entity by ID (from tenant-specific database)
     */
    public Optional<T> findById(String id) {
        T entity = getMongoTemplate().findById(id, entityClass);
        return Optional.ofNullable(entity);
    }
    
    /**
     * Save entity (to tenant-specific database)
     */
    public T save(T entity) {
        return getMongoTemplate().save(entity);
    }
    
    /**
     * Delete entity by ID (from tenant-specific database)
     */
    public void deleteById(String id) {
        Query query = new Query(Criteria.where("id").is(id));
        getMongoTemplate().remove(query, entityClass);
    }
    
    /**
     * Check if entity exists by ID (in tenant-specific database)
     */
    public boolean existsById(String id) {
        Query query = new Query(Criteria.where("id").is(id));
        return getMongoTemplate().exists(query, entityClass);
    }
    
    /**
     * Count entities (in tenant-specific database)
     */
    public long count() {
        return getMongoTemplate().count(new Query(), entityClass);
    }
    
    /**
     * Find entities by field value (in tenant-specific database)
     */
    protected List<T> findByField(String fieldName, Object value) {
        Query query = new Query(Criteria.where(fieldName).is(value));
        return getMongoTemplate().find(query, entityClass);
    }
    
    /**
     * Find entities by multiple criteria (in tenant-specific database)
     */
    protected List<T> findByCriteria(Criteria... criteria) {
        Query query = new Query();
        for (Criteria criterion : criteria) {
            query.addCriteria(criterion);
        }
        return getMongoTemplate().find(query, entityClass);
    }
}
