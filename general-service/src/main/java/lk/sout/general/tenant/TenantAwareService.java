package lk.sout.general.tenant;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

/**
 * Base service class that provides tenant-aware database operations
 */
@Component
public class TenantAwareService {

    @Autowired
    private TenantDatabaseService tenantDatabaseService;

    /**
     * Get MongoTemplate for current tenant
     */
    protected MongoTemplate getMongoTemplate() {
        return tenantDatabaseService.getCurrentTenantMongoTemplate();
    }

    /**
     * Get MongoTemplate for specific tenant
     */
    protected MongoTemplate getMongoTemplate(String tenant) {
        return tenantDatabaseService.getMongoTemplateForTenant(tenant);
    }
}
