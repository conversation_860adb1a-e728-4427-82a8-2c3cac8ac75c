package lk.sout.general.tenant.controller;

import lk.sout.general.tenant.entity.Tenant;
import lk.sout.general.tenant.service.TenantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/tenant")
@CrossOrigin
public class TenantController {
    
    @Autowired
    private TenantService tenantService;
    
    /**
     * Create a new tenant
     */
    @PostMapping("/create")
    public ResponseEntity<?> createTenant(@RequestBody Tenant tenant) {
        try {
            Tenant createdTenant = tenantService.createTenant(tenant);
            return ResponseEntity.ok(createdTenant);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error creating tenant: " + e.getMessage());
        }
    }
    
    /**
     * Get tenant by ID
     */
    @GetMapping("/{tenantId}")
    public ResponseEntity<?> getTenant(@PathVariable String tenantId) {
        try {
            Optional<Tenant> tenant = tenantService.getTenantById(tenantId);
            if (tenant.isPresent()) {
                return ResponseEntity.ok(tenant.get());
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error getting tenant: " + e.getMessage());
        }
    }
    
    /**
     * Get all active tenants
     */
    @GetMapping("/active")
    public ResponseEntity<?> getActiveTenants() {
        try {
            List<Tenant> tenants = tenantService.getAllActiveTenants();
            return ResponseEntity.ok(tenants);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error getting tenants: " + e.getMessage());
        }
    }
    
    /**
     * Update tenant
     */
    @PutMapping("/update")
    public ResponseEntity<?> updateTenant(@RequestBody Tenant tenant) {
        try {
            Tenant updatedTenant = tenantService.updateTenant(tenant);
            return ResponseEntity.ok(updatedTenant);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error updating tenant: " + e.getMessage());
        }
    }
    
    /**
     * Activate/Deactivate tenant
     */
    @PutMapping("/{tenantId}/active/{active}")
    public ResponseEntity<?> setTenantActive(@PathVariable String tenantId, @PathVariable boolean active) {
        try {
            tenantService.setTenantActive(tenantId, active);
            return ResponseEntity.ok("Tenant status updated successfully");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error updating tenant status: " + e.getMessage());
        }
    }
    
    /**
     * Get current tenant
     */
    @GetMapping("/current")
    public ResponseEntity<?> getCurrentTenant() {
        try {
            Optional<Tenant> tenant = tenantService.getCurrentTenant();
            if (tenant.isPresent()) {
                return ResponseEntity.ok(tenant.get());
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error getting current tenant: " + e.getMessage());
        }
    }
    
    /**
     * Check if tenant is valid
     */
    @GetMapping("/{tenantId}/valid")
    public ResponseEntity<?> isTenantValid(@PathVariable String tenantId) {
        try {
            boolean isValid = tenantService.isValidTenant(tenantId);
            return ResponseEntity.ok(isValid);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error checking tenant validity: " + e.getMessage());
        }
    }

    /**
     * Test database connection for tenant
     */
    @GetMapping("/{tenantId}/test-connection")
    public ResponseEntity<?> testDatabaseConnection(@PathVariable String tenantId) {
        try {
            boolean connectionOk = tenantService.testTenantDatabaseConnection(tenantId);
            return ResponseEntity.ok(Map.of("connectionOk", connectionOk));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error testing database connection: " + e.getMessage());
        }
    }

    /**
     * Refresh database connection for tenant
     */
    @PostMapping("/{tenantId}/refresh-connection")
    public ResponseEntity<?> refreshDatabaseConnection(@PathVariable String tenantId) {
        try {
            tenantService.refreshTenantDatabaseConnection(tenantId);
            return ResponseEntity.ok("Database connection refreshed successfully");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error refreshing database connection: " + e.getMessage());
        }
    }
}
