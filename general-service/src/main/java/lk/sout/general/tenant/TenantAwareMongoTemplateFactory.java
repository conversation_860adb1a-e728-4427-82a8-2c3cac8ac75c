package lk.sout.general.tenant;

import com.mongodb.client.MongoClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.core.MongoTemplate;

/**
 * Factory that creates a tenant-aware MongoTemplate
 * This is a simpler approach than extending MongoTemplate
 */
@Configuration
public class TenantAwareMongoTemplateFactory {
    
    @Autowired
    private TenantDatabaseService tenantDatabaseService;
    
    @Bean
    @Primary
    public MongoTemplate tenantAwareMongoTemplate(@Qualifier("defaultMongoClient") MongoClient mongoClient) {
        return new MongoTemplate(mongoClient, "generalWebDemo") {
            
            // Override key methods to use tenant-specific template
            
            @Override
            public <T> java.util.List<T> findAll(Class<T> entityClass) {
                return getCurrentTenantTemplate().findAll(entityClass);
            }
            
            @Override
            public <T> T findById(Object id, Class<T> entityClass) {
                return getCurrentTenantTemplate().findById(id, entityClass);
            }
            
            @Override
            public <T> java.util.List<T> find(org.springframework.data.mongodb.core.query.Query query, Class<T> entityClass) {
                return getCurrentTenantTemplate().find(query, entityClass);
            }
            
            @Override
            public <T> T findOne(org.springframework.data.mongodb.core.query.Query query, Class<T> entityClass) {
                return getCurrentTenantTemplate().findOne(query, entityClass);
            }
            
            @Override
            public <T> T save(T objectToSave) {
                return getCurrentTenantTemplate().save(objectToSave);
            }
            
            @Override
            public <T> T insert(T objectToSave) {
                return getCurrentTenantTemplate().insert(objectToSave);
            }
            
            @Override
            public void remove(Object object) {
                getCurrentTenantTemplate().remove(object);
            }
            
            @Override
            public long count(org.springframework.data.mongodb.core.query.Query query, Class<?> entityClass) {
                return getCurrentTenantTemplate().count(query, entityClass);
            }
            
            @Override
            public boolean exists(org.springframework.data.mongodb.core.query.Query query, Class<?> entityClass) {
                return getCurrentTenantTemplate().exists(query, entityClass);
            }
            
            @Override
            public <T> T findAndModify(org.springframework.data.mongodb.core.query.Query query, 
                                     org.springframework.data.mongodb.core.query.Update update, 
                                     Class<T> entityClass) {
                return getCurrentTenantTemplate().findAndModify(query, update, entityClass);
            }
            
            @Override
            public <T> T findAndRemove(org.springframework.data.mongodb.core.query.Query query, Class<T> entityClass) {
                return getCurrentTenantTemplate().findAndRemove(query, entityClass);
            }
            
            /**
             * Get the current tenant's MongoTemplate
             */
            private MongoTemplate getCurrentTenantTemplate() {
                try {
                    String currentTenant = TenantContext.getCurrentTenant();
                    if (currentTenant != null && !currentTenant.isEmpty()) {
                        return tenantDatabaseService.getCurrentTenantMongoTemplate();
                    }
                } catch (Exception e) {
                    // Log error but continue with fallback
                    System.err.println("Error getting tenant template: " + e.getMessage());
                }
                // Fallback to default template
                return super.mongoTemplate();
            }
            
            private MongoTemplate mongoTemplate() {
                return this;
            }
        };
    }
}
