package lk.sout.general.tenant.controller;

import lk.sout.general.tenant.TenantContext;
import lk.sout.general.tenant.TenantDatabaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Test controller for tenant functionality
 */
@RestController
@RequestMapping("/tenant-test")
@CrossOrigin
public class TenantTestController {
    
    @Autowired
    private TenantDatabaseService tenantDatabaseService;
    
    /**
     * Test tenant resolution and database connection
     */
    @GetMapping("/info")
    public ResponseEntity<?> getTenantInfo() {
        try {
            Map<String, Object> info = new HashMap<>();
            
            // Current tenant from context
            String currentTenant = TenantContext.getCurrentTenant();
            info.put("currentTenant", currentTenant);
            
            // Database information
            MongoTemplate template = tenantDatabaseService.getCurrentTenantMongoTemplate();
            String dbName = template.getDb().getName();
            info.put("connectedDatabase", dbName);
            
            // Test database ping
            try {
                template.getDb().runCommand(new org.bson.Document("ping", 1));
                info.put("databaseConnectionStatus", "OK");
            } catch (Exception e) {
                info.put("databaseConnectionStatus", "FAILED");
                info.put("databaseError", e.getMessage());
            }
            
            // Show pattern
            info.put("databasePattern", "generalWeb + " + (currentTenant != null ? currentTenant : "default"));
            
            return ResponseEntity.ok(info);
            
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Failed to get tenant info");
            error.put("message", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
    
    /**
     * Test database operations
     */
    @GetMapping("/test-db")
    public ResponseEntity<?> testDatabaseOperations() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            String currentTenant = TenantContext.getCurrentTenant();
            result.put("tenant", currentTenant);
            
            MongoTemplate template = tenantDatabaseService.getCurrentTenantMongoTemplate();
            String dbName = template.getDb().getName();
            result.put("database", dbName);
            
            // Test collection operations
            try {
                // List collections
                var collections = template.getCollectionNames();
                result.put("collections", collections);
                result.put("collectionCount", collections.size());
                
                // Test if we can access user collection
                if (collections.contains("user")) {
                    long userCount = template.count(new org.springframework.data.mongodb.core.query.Query(), "user");
                    result.put("userCount", userCount);
                }
                
                // Test if we can access item collection
                if (collections.contains("item")) {
                    long itemCount = template.count(new org.springframework.data.mongodb.core.query.Query(), "item");
                    result.put("itemCount", itemCount);
                }
                
                result.put("status", "SUCCESS");
                
            } catch (Exception e) {
                result.put("status", "FAILED");
                result.put("error", e.getMessage());
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Database test failed");
            error.put("message", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
    
    /**
     * Test with specific tenant
     */
    @GetMapping("/test-tenant/{tenant}")
    public ResponseEntity<?> testSpecificTenant(@PathVariable String tenant) {
        try {
            Map<String, Object> result = new HashMap<>();
            
            MongoTemplate template = tenantDatabaseService.getMongoTemplateForTenant(tenant);
            String dbName = template.getDb().getName();
            
            result.put("requestedTenant", tenant);
            result.put("databaseName", dbName);
            result.put("pattern", "generalWeb + " + tenant);
            
            // Test connection
            try {
                template.getDb().runCommand(new org.bson.Document("ping", 1));
                result.put("connectionStatus", "OK");
            } catch (Exception e) {
                result.put("connectionStatus", "FAILED");
                result.put("connectionError", e.getMessage());
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Failed to test tenant: " + tenant);
            error.put("message", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
}
