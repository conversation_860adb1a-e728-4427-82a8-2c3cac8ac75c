package lk.sout.general.tenant.controller;

import lk.sout.general.tenant.context.TenantContext;
import lk.sout.general.tenant.database.TenantMongoTemplateProvider;
import lk.sout.general.tenant.entity.Tenant;
import lk.sout.general.tenant.service.TenantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/tenant-test")
@CrossOrigin
public class TenantTestController {
    
    @Autowired
    private TenantService tenantService;
    
    @Autowired
    private TenantMongoTemplateProvider templateProvider;
    
    /**
     * Test tenant resolution and database connection
     */
    @GetMapping("/info")
    public ResponseEntity<?> getTenantInfo() {
        try {
            Map<String, Object> info = new HashMap<>();
            
            // Current tenant from context
            String currentTenantId = TenantContext.getCurrentTenant();
            info.put("currentTenantId", currentTenantId);
            
            // Tenant details
            Optional<Tenant> tenant = tenantService.getCurrentTenant();
            if (tenant.isPresent()) {
                Tenant t = tenant.get();
                info.put("tenantName", t.getName());
                info.put("databaseName", t.getDatabaseName());
                info.put("subdomain", t.getSubdomain());
                info.put("active", t.isActive());
            } else {
                info.put("tenantDetails", "Tenant not found");
            }
            
            // Database connection test
            try {
                MongoTemplate template = templateProvider.getCurrentTenantTemplate();
                String dbName = template.getDb().getName();
                info.put("connectedDatabase", dbName);
                
                // Test database ping
                template.getDb().runCommand(new org.bson.Document("ping", 1));
                info.put("databaseConnectionStatus", "OK");
                
            } catch (Exception e) {
                info.put("databaseConnectionStatus", "FAILED");
                info.put("databaseError", e.getMessage());
            }
            
            return ResponseEntity.ok(info);
            
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Failed to get tenant info");
            error.put("message", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
    
    /**
     * Test database operations
     */
    @GetMapping("/test-db")
    public ResponseEntity<?> testDatabaseOperations() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            String currentTenantId = TenantContext.getCurrentTenant();
            result.put("tenantId", currentTenantId);
            
            MongoTemplate template = templateProvider.getCurrentTenantTemplate();
            String dbName = template.getDb().getName();
            result.put("database", dbName);
            
            // Test collection operations
            try {
                // List collections
                var collections = template.getCollectionNames();
                result.put("collections", collections);
                result.put("collectionCount", collections.size());
                
                // Test if we can access user collection
                if (collections.contains("user")) {
                    long userCount = template.count(new org.springframework.data.mongodb.core.query.Query(), "user");
                    result.put("userCount", userCount);
                }
                
                // Test if we can access item collection
                if (collections.contains("item")) {
                    long itemCount = template.count(new org.springframework.data.mongodb.core.query.Query(), "item");
                    result.put("itemCount", itemCount);
                }
                
                result.put("status", "SUCCESS");
                
            } catch (Exception e) {
                result.put("status", "FAILED");
                result.put("error", e.getMessage());
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Database test failed");
            error.put("message", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
    
    /**
     * Get all configured tenants
     */
    @GetMapping("/all-tenants")
    public ResponseEntity<?> getAllTenants() {
        try {
            var tenants = tenantService.getAllActiveTenants();
            return ResponseEntity.ok(tenants);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Failed to get tenants");
            error.put("message", e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
}
