package lk.sout.general.tenant.config;

import lk.sout.general.tenant.interceptor.TenantInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@EnableCaching
public class TenantConfig implements WebMvcConfigurer {
    
    @Autowired
    private TenantInterceptor tenantInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tenantInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(
                    "/login/**",
                    "/actuator/**",
                    "/health/**",
                    "/error/**",
                    "/favicon.ico",
                    "/static/**",
                    "/public/**"
                );
    }
    
    @Bean
    public CacheManager cacheManager() {
        return new ConcurrentMapCacheManager("tenants", "tenant-validity");
    }
}
