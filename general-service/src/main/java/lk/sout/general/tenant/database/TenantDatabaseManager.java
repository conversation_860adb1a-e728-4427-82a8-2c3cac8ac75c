package lk.sout.general.tenant.database;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoDatabase;
import lk.sout.general.tenant.entity.Tenant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * Manages database connections for different tenants
 */
@Component
public class TenantDatabaseManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(TenantDatabaseManager.class);
    
    // Cache for MongoTemplate instances per tenant
    private final Map<String, MongoTemplate> tenantTemplates = new ConcurrentHashMap<>();
    
    // Cache for MongoClient instances per tenant
    private final Map<String, MongoClient> tenantClients = new ConcurrentHashMap<>();
    
    // Default database configuration
    private static final String DEFAULT_HOST = "localhost";
    private static final int DEFAULT_PORT = 27017;
    
    /**
     * Get MongoTemplate for a specific tenant
     */
    public MongoTemplate getMongoTemplate(String tenantId, Tenant tenant) {
        return tenantTemplates.computeIfAbsent(tenantId, k -> createMongoTemplate(tenant));
    }
    
    /**
     * Create MongoTemplate for tenant
     */
    private MongoTemplate createMongoTemplate(Tenant tenant) {
        try {
            MongoClient mongoClient = createMongoClient(tenant);
            MongoDatabase database = mongoClient.getDatabase(tenant.getDatabaseName());
            
            MongoTemplate template = new MongoTemplate(mongoClient, tenant.getDatabaseName());
            
            LOGGER.info("Created MongoTemplate for tenant: {} with database: {}", 
                       tenant.getTenantId(), tenant.getDatabaseName());
            
            return template;
            
        } catch (Exception e) {
            LOGGER.error("Failed to create MongoTemplate for tenant: {}", tenant.getTenantId(), e);
            throw new RuntimeException("Failed to create database connection for tenant: " + tenant.getTenantId(), e);
        }
    }
    
    /**
     * Create MongoClient for tenant
     */
    private MongoClient createMongoClient(Tenant tenant) {
        String tenantId = tenant.getTenantId();
        
        return tenantClients.computeIfAbsent(tenantId, k -> {
            try {
                String connectionString = buildConnectionString(tenant);
                LOGGER.info("Creating MongoClient for tenant: {} with connection: {}", 
                           tenantId, maskPassword(connectionString));
                
                return MongoClients.create(connectionString);
                
            } catch (Exception e) {
                LOGGER.error("Failed to create MongoClient for tenant: {}", tenantId, e);
                throw new RuntimeException("Failed to create MongoDB client for tenant: " + tenantId, e);
            }
        });
    }
    
    /**
     * Build MongoDB connection string for tenant
     */
    private String buildConnectionString(Tenant tenant) {
        // If custom connection string is provided, use it
        if (tenant.getConnectionString() != null && !tenant.getConnectionString().trim().isEmpty()) {
            return tenant.getConnectionString();
        }
        
        // Build connection string from individual components
        StringBuilder connectionString = new StringBuilder("mongodb://");
        
        // Add authentication if provided
        if (tenant.getDatabaseUsername() != null && !tenant.getDatabaseUsername().trim().isEmpty()) {
            connectionString.append(tenant.getDatabaseUsername());
            if (tenant.getDatabasePassword() != null && !tenant.getDatabasePassword().trim().isEmpty()) {
                connectionString.append(":").append(tenant.getDatabasePassword());
            }
            connectionString.append("@");
        }
        
        // Add host and port
        String host = tenant.getDatabaseHost() != null ? tenant.getDatabaseHost() : DEFAULT_HOST;
        int port = tenant.getDatabasePort() > 0 ? tenant.getDatabasePort() : DEFAULT_PORT;
        
        connectionString.append(host).append(":").append(port);
        
        // Add database name
        connectionString.append("/").append(tenant.getDatabaseName());
        
        return connectionString.toString();
    }
    
    /**
     * Mask password in connection string for logging
     */
    private String maskPassword(String connectionString) {
        return connectionString.replaceAll("://([^:]+):([^@]+)@", "://$1:****@");
    }
    
    /**
     * Remove tenant from cache (when tenant is deleted or updated)
     */
    public void removeTenant(String tenantId) {
        try {
            // Close and remove MongoTemplate
            MongoTemplate template = tenantTemplates.remove(tenantId);
            if (template != null) {
                LOGGER.info("Removed MongoTemplate for tenant: {}", tenantId);
            }
            
            // Close and remove MongoClient
            MongoClient client = tenantClients.remove(tenantId);
            if (client != null) {
                client.close();
                LOGGER.info("Closed MongoClient for tenant: {}", tenantId);
            }
            
        } catch (Exception e) {
            LOGGER.error("Error removing tenant from cache: {}", tenantId, e);
        }
    }
    
    /**
     * Test database connection for tenant
     */
    public boolean testConnection(Tenant tenant) {
        try {
            MongoClient testClient = null;
            try {
                String connectionString = buildConnectionString(tenant);
                testClient = MongoClients.create(connectionString);
                
                // Try to ping the database
                MongoDatabase database = testClient.getDatabase(tenant.getDatabaseName());
                database.runCommand(new org.bson.Document("ping", 1));
                
                LOGGER.info("Database connection test successful for tenant: {}", tenant.getTenantId());
                return true;
                
            } finally {
                if (testClient != null) {
                    testClient.close();
                }
            }
            
        } catch (Exception e) {
            LOGGER.error("Database connection test failed for tenant: {}", tenant.getTenantId(), e);
            return false;
        }
    }
    
    /**
     * Get all cached tenant IDs
     */
    public java.util.Set<String> getCachedTenants() {
        return tenantTemplates.keySet();
    }
    
    /**
     * Clear all cached connections
     */
    public void clearAllConnections() {
        LOGGER.info("Clearing all tenant database connections");
        
        // Close all clients
        tenantClients.values().forEach(client -> {
            try {
                client.close();
            } catch (Exception e) {
                LOGGER.error("Error closing MongoClient", e);
            }
        });
        
        // Clear caches
        tenantTemplates.clear();
        tenantClients.clear();
        
        LOGGER.info("All tenant database connections cleared");
    }
}
