# Multi-Tenant Support - Database-per-Tenant

This module provides simple and manageable multi-tenant support with **complete database isolation**.

## Architecture: 1 Backend + Many Frontends + Many Databases

- **Single Java Backend**: One backend deployment handles all tenants
- **Multiple Angular Frontends**: Each tenant gets their own frontend deployment
- **Separate Databases**: Each tenant has their own MongoDB database for complete data isolation

## How It Works

1. **Tenant Identification**: Tenants are identified by:
   - HTTP Header: `X-Tenant-ID`
   - Subdomain: `tenant1.yourdomain.com`
   - URL Path: `/api/tenant1/...`

2. **Database Isolation**: Each tenant has their own MongoDB database
   - Complete data separation
   - No shared collections
   - Independent scaling per tenant

3. **Automatic Context**: The system automatically routes to the correct database based on tenant context

## Setup

### 1. Make Entities Tenant-Aware

Extend your entities from `TenantAware`:

```java
@Document
public class YourEntity extends TenantAware {
    // Your entity fields
}
```

### 2. Use Tenant-Aware Repositories

For custom repositories, extend `TenantAwareRepository`:

```java
@Repository
public class YourEntityRepository extends TenantAwareRepository<YourEntity> {

    public YourEntityRepository() {
        super(YourEntity.class);
    }

    // Custom methods automatically use tenant-specific database
    public List<YourEntity> findByName(String name) {
        return findByField("name", name);
    }
}
```

## Frontend Integration

### Angular Deployment

Each tenant gets their own Angular deployment with different configurations:

```typescript
// environment.tenant1.ts
export const environment = {
  production: true,
  apiUrl: 'https://api.yourdomain.com/',
  tenantId: 'tenant1'
};

// environment.tenant2.ts
export const environment = {
  production: true,
  apiUrl: 'https://api.yourdomain.com/',
  tenantId: 'tenant2'
};
```

### HTTP Interceptor

Add tenant ID to all requests:

```typescript
@Injectable()
export class TenantInterceptor implements HttpInterceptor {
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const tenantId = environment.tenantId;
    
    if (tenantId) {
      req = req.clone({
        setHeaders: {
          'X-Tenant-ID': tenantId
        }
      });
    }
    
    return next.handle(req);
  }
}
```

## API Usage

### Create Tenant with Database Configuration

```bash
POST /tenant/create
{
  "tenantId": "tenant1",
  "subdomain": "tenant1",
  "name": "Tenant One",
  "contactEmail": "<EMAIL>",
  "maxUsers": 50,
  "databaseName": "tenant1_db",
  "databaseHost": "localhost",
  "databasePort": 27017,
  "databaseUsername": "tenant1_user",
  "databasePassword": "secure_password"
}
```

Or with connection string:

```bash
POST /tenant/create
{
  "tenantId": "tenant1",
  "subdomain": "tenant1",
  "name": "Tenant One",
  "contactEmail": "<EMAIL>",
  "maxUsers": 50,
  "databaseName": "tenant1_db",
  "connectionString": "*****************************************************************"
}
```

### Check Current Tenant

```bash
GET /tenant/current
Headers: X-Tenant-ID: tenant1
```

### Test Database Connection

```bash
GET /tenant/tenant1/test-connection
```

### Refresh Database Connection

```bash
POST /tenant/tenant1/refresh-connection
```

## Deployment Strategy

### Single Backend
- One Java backend handles all tenants
- Automatic database routing based on tenant context
- Shared application logic and infrastructure

### Multiple Databases
- Each tenant has their own MongoDB database
- Complete data isolation and security
- Independent scaling and backup strategies
- No shared collections or data

### Multiple Frontend Deployments
- Each tenant gets their own Angular build
- Different subdomains: tenant1.yourdomain.com, tenant2.yourdomain.com
- Tenant-specific configurations and branding

### Your Current Setup

**Demo Environment:**
- Frontend: `demo.vaganana.com`
- Database: `generalWebDemo`

**Wanigarathna Environment:**
- Frontend: `wanigarathna.vaganana.com`
- Database: `generalWebWanigarathna`

### Example Nginx Configuration

```nginx
server {
    server_name demo.vaganana.com;
    root /var/www/demo;

    location /api/ {
        proxy_pass http://backend;
        # Tenant automatically resolved from subdomain
    }
}

server {
    server_name wanigarathna.vaganana.com;
    root /var/www/wanigarathna;

    location /api/ {
        proxy_pass http://backend;
        # Tenant automatically resolved from subdomain
    }
}
```

## Benefits

1. **Complete Isolation**: Each tenant has their own database - no data mixing possible
2. **Simple & Manageable**: Single backend codebase, easy to understand
3. **Scalable**: Add new tenants without code changes, scale databases independently
4. **Secure**: Database-level isolation provides maximum security
5. **Flexible**: Support for different database configurations per tenant
6. **Performance**: No cross-tenant queries, optimized performance per tenant

## Security

- Tenant context is automatically set and cleared for each request
- Complete database isolation - no shared data structures
- No cross-tenant data access possible at database level
- Tenant validation on each request
- Database credentials can be different per tenant

## Monitoring

- Each tenant's database usage can be tracked separately
- Database performance metrics per tenant
- Independent backup and restore strategies
- Subscription limits can be enforced per database
- Connection pooling and caching per tenant
