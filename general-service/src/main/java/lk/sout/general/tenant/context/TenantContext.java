package lk.sout.general.tenant.context;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Thread-local storage for tenant context
 */
public class TenantContext {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(TenantContext.class);
    
    private static final ThreadLocal<String> CURRENT_TENANT = new ThreadLocal<>();
    private static final String DEFAULT_TENANT = "default";
    
    /**
     * Set the current tenant ID for the current thread
     */
    public static void setCurrentTenant(String tenantId) {
        if (tenantId == null || tenantId.trim().isEmpty()) {
            tenantId = DEFAULT_TENANT;
        }
        LOGGER.debug("Setting tenant context to: {}", tenantId);
        CURRENT_TENANT.set(tenantId);
    }
    
    /**
     * Get the current tenant ID for the current thread
     */
    public static String getCurrentTenant() {
        String tenantId = CURRENT_TENANT.get();
        if (tenantId == null) {
            tenantId = DEFAULT_TENANT;
            setCurrentTenant(tenantId);
        }
        return tenantId;
    }
    
    /**
     * Clear the tenant context for the current thread
     */
    public static void clear() {
        LOGGER.debug("Clearing tenant context");
        CURRENT_TENANT.remove();
    }
    
    /**
     * Check if tenant context is set
     */
    public static boolean isSet() {
        return CURRENT_TENANT.get() != null;
    }
    
    /**
     * Get default tenant ID
     */
    public static String getDefaultTenant() {
        return DEFAULT_TENANT;
    }
}
