package lk.sout.general.tenant.service.impl;

import lk.sout.general.tenant.context.TenantContext;
import lk.sout.general.tenant.database.TenantDatabaseManager;
import lk.sout.general.tenant.entity.Tenant;
import lk.sout.general.tenant.repository.TenantRepository;
import lk.sout.general.tenant.service.TenantService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class TenantServiceImpl implements TenantService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(TenantServiceImpl.class);
    
    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private TenantDatabaseManager databaseManager;
    
    @Override
    public Tenant createTenant(Tenant tenant) {
        try {
            // Validate tenant data
            if (tenant.getTenantId() == null || tenant.getTenantId().trim().isEmpty()) {
                throw new IllegalArgumentException("Tenant ID is required");
            }
            
            if (tenant.getSubdomain() == null || tenant.getSubdomain().trim().isEmpty()) {
                throw new IllegalArgumentException("Subdomain is required");
            }
            
            // Check if tenant already exists
            if (tenantRepository.findByTenantId(tenant.getTenantId()).isPresent()) {
                throw new IllegalArgumentException("Tenant with ID " + tenant.getTenantId() + " already exists");
            }
            
            if (tenantRepository.findBySubdomain(tenant.getSubdomain()).isPresent()) {
                throw new IllegalArgumentException("Tenant with subdomain " + tenant.getSubdomain() + " already exists");
            }
            
            // Set default values
            tenant.setCreatedDate(LocalDateTime.now());
            tenant.setLastModifiedDate(LocalDateTime.now());
            tenant.setActive(true);
            
            if (tenant.getMaxUsers() <= 0) {
                tenant.setMaxUsers(10); // Default max users
            }
            
            Tenant savedTenant = tenantRepository.save(tenant);
            LOGGER.info("Created new tenant: {}", savedTenant.getTenantId());

            // Test database connection
            if (!databaseManager.testConnection(savedTenant)) {
                LOGGER.warn("Database connection test failed for new tenant: {}", savedTenant.getTenantId());
            }

            return savedTenant;
            
        } catch (Exception e) {
            LOGGER.error("Error creating tenant: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    @Override
    @Cacheable(value = "tenants", key = "#tenantId")
    public Optional<Tenant> getTenantById(String tenantId) {
        try {
            return tenantRepository.findByTenantId(tenantId);
        } catch (Exception e) {
            LOGGER.error("Error getting tenant by ID: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }
    
    @Override
    @Cacheable(value = "tenants", key = "#subdomain")
    public Optional<Tenant> getTenantBySubdomain(String subdomain) {
        try {
            return tenantRepository.findBySubdomain(subdomain);
        } catch (Exception e) {
            LOGGER.error("Error getting tenant by subdomain: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }
    
    @Override
    public List<Tenant> getAllActiveTenants() {
        try {
            return tenantRepository.findByActiveTrue();
        } catch (Exception e) {
            LOGGER.error("Error getting active tenants: {}", e.getMessage(), e);
            return List.of();
        }
    }
    
    @Override
    public Tenant updateTenant(Tenant tenant) {
        try {
            Optional<Tenant> existingTenant = tenantRepository.findByTenantId(tenant.getTenantId());
            if (existingTenant.isEmpty()) {
                throw new IllegalArgumentException("Tenant not found: " + tenant.getTenantId());
            }
            
            tenant.setLastModifiedDate(LocalDateTime.now());
            Tenant updatedTenant = tenantRepository.save(tenant);
            LOGGER.info("Updated tenant: {}", updatedTenant.getTenantId());

            // Refresh database connection if database config changed
            databaseManager.removeTenant(updatedTenant.getTenantId());

            return updatedTenant;
            
        } catch (Exception e) {
            LOGGER.error("Error updating tenant: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    @Override
    public void setTenantActive(String tenantId, boolean active) {
        try {
            Optional<Tenant> tenantOpt = tenantRepository.findByTenantId(tenantId);
            if (tenantOpt.isPresent()) {
                Tenant tenant = tenantOpt.get();
                tenant.setActive(active);
                tenant.setLastModifiedDate(LocalDateTime.now());
                tenantRepository.save(tenant);
                LOGGER.info("Set tenant {} active status to: {}", tenantId, active);
            }
        } catch (Exception e) {
            LOGGER.error("Error setting tenant active status: {}", e.getMessage(), e);
        }
    }
    
    @Override
    @Cacheable(value = "tenant-validity", key = "#tenantId")
    public boolean isValidTenant(String tenantId) {
        try {
            if ("default".equals(tenantId)) {
                return true; // Default tenant is always valid
            }
            
            Optional<Tenant> tenant = getTenantById(tenantId);
            return tenant.isPresent() && tenant.get().isActive();
            
        } catch (Exception e) {
            LOGGER.error("Error checking tenant validity: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public void deleteTenant(String tenantId) {
        try {
            Optional<Tenant> tenantOpt = tenantRepository.findByTenantId(tenantId);
            if (tenantOpt.isPresent()) {
                Tenant tenant = tenantOpt.get();
                tenant.setActive(false);
                tenant.setLastModifiedDate(LocalDateTime.now());
                tenantRepository.save(tenant);
                LOGGER.info("Soft deleted tenant: {}", tenantId);
            }
        } catch (Exception e) {
            LOGGER.error("Error deleting tenant: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public String getCurrentTenantId() {
        return TenantContext.getCurrentTenant();
    }
    
    @Override
    public Optional<Tenant> getCurrentTenant() {
        String tenantId = getCurrentTenantId();
        if ("default".equals(tenantId)) {
            // Return a default tenant object
            Tenant defaultTenant = new Tenant("default", "default", "Default Tenant");
            return Optional.of(defaultTenant);
        }
        return getTenantById(tenantId);
    }

    @Override
    public boolean testTenantDatabaseConnection(String tenantId) {
        try {
            Optional<Tenant> tenantOpt = getTenantById(tenantId);
            if (tenantOpt.isEmpty()) {
                return false;
            }

            return databaseManager.testConnection(tenantOpt.get());

        } catch (Exception e) {
            LOGGER.error("Error testing database connection for tenant: {}", tenantId, e);
            return false;
        }
    }

    @Override
    public void refreshTenantDatabaseConnection(String tenantId) {
        try {
            databaseManager.removeTenant(tenantId);
            LOGGER.info("Refreshed database connection for tenant: {}", tenantId);
        } catch (Exception e) {
            LOGGER.error("Error refreshing database connection for tenant: {}", tenantId, e);
        }
    }
}
