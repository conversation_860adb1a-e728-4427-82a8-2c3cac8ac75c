package lk.sout.general.tenant;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;

/**
 * Service to manage tenant-specific MongoDB templates
 */
@Service
public class TenantDatabaseService {

    @Value("${spring.data.mongodb.host}")
    private String host;

    @Value("${spring.data.mongodb.port}")
    private int port;

    @Value("${spring.data.mongodb.username}")
    private String username;

    @Value("${spring.data.mongodb.password}")
    private String password;

    private final ConcurrentHashMap<String, MongoTemplate> mongoTemplateCache = new ConcurrentHashMap<>();

    /**
     * Get MongoTemplate for current tenant
     */
    public MongoTemplate getCurrentTenantMongoTemplate() {
        String tenant = TenantContext.getCurrentTenant();
        if (tenant == null || tenant.equals("default")) {
            tenant = "default";
        }
        return getMongoTemplateForTenant(tenant);
    }

    /**
     * Get MongoTemplate for specific tenant
     */
    public MongoTemplate getMongoTemplateForTenant(String tenant) {
        return mongoTemplateCache.computeIfAbsent(tenant, this::createMongoTemplate);
    }

    private MongoTemplate createMongoTemplate(String tenant) {
        String databaseName = "generalWeb" + capitalizeFirst(tenant);
        String authDatabase = "generalWebDemo"; // Use shared auth database
        String connectionString = String.format("mongodb://%s:%s@%s:%d/%s?authSource=%s",
                username, password, host, port, databaseName, authDatabase);

        MongoClient mongoClient = MongoClients.create(connectionString);
        SimpleMongoClientDatabaseFactory factory = new SimpleMongoClientDatabaseFactory(mongoClient, databaseName);
        return new MongoTemplate(factory);
    }

    /**
     * Capitalize first letter of tenant name for database naming consistency
     */
    private String capitalizeFirst(String tenant) {
        if (tenant == null || tenant.isEmpty()) {
            return tenant;
        }
        if (tenant.equals("default")) {
            return ""; // For default tenant, use just "generalWeb"
        }
        return tenant.substring(0, 1).toUpperCase() + tenant.substring(1).toLowerCase();
    }
}
