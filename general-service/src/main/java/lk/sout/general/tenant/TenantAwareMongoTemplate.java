package lk.sout.general.tenant;

import com.mongodb.client.MongoClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.core.FindAndModifyOptions;

import java.util.Collection;
import java.util.List;

/**
 * Tenant-aware MongoTemplate that automatically routes operations to the correct tenant database
 * This allows existing services to work without modification
 */
public class TenantAwareMongoTemplate extends MongoTemplate {
    
    @Autowired
    private TenantDatabaseService tenantDatabaseService;
    
    public TenantAwareMongoTemplate(MongoClient mongoClient, String databaseName) {
        super(mongoClient, databaseName);
    }
    
    /**
     * Get the current tenant's MongoTemplate
     */
    private MongoTemplate getCurrentTenantTemplate() {
        try {
            return tenantDatabaseService.getCurrentTenantMongoTemplate();
        } catch (Exception e) {
            // Fallback to default template if tenant resolution fails
            return super.getMongoDbFactory().getMongoDatabase().getName().equals("generalWebDemo") ? this : this;
        }
    }
    
    // Override all major MongoTemplate methods to use tenant-specific template
    
    @Override
    public <T> List<T> findAll(Class<T> entityClass) {
        return getCurrentTenantTemplate().findAll(entityClass);
    }
    
    @Override
    public <T> List<T> findAll(Class<T> entityClass, String collectionName) {
        return getCurrentTenantTemplate().findAll(entityClass, collectionName);
    }
    
    @Override
    public <T> T findById(Object id, Class<T> entityClass) {
        return getCurrentTenantTemplate().findById(id, entityClass);
    }
    
    @Override
    public <T> T findById(Object id, Class<T> entityClass, String collectionName) {
        return getCurrentTenantTemplate().findById(id, entityClass, collectionName);
    }
    
    @Override
    public <T> T findOne(Query query, Class<T> entityClass) {
        return getCurrentTenantTemplate().findOne(query, entityClass);
    }
    
    @Override
    public <T> T findOne(Query query, Class<T> entityClass, String collectionName) {
        return getCurrentTenantTemplate().findOne(query, entityClass, collectionName);
    }
    
    @Override
    public <T> List<T> find(Query query, Class<T> entityClass) {
        return getCurrentTenantTemplate().find(query, entityClass);
    }
    
    @Override
    public <T> List<T> find(Query query, Class<T> entityClass, String collectionName) {
        return getCurrentTenantTemplate().find(query, entityClass, collectionName);
    }
    
    @Override
    public <T> T save(T objectToSave) {
        return getCurrentTenantTemplate().save(objectToSave);
    }
    
    @Override
    public <T> T save(T objectToSave, String collectionName) {
        return getCurrentTenantTemplate().save(objectToSave, collectionName);
    }
    
    @Override
    public <T> T insert(T objectToSave) {
        return getCurrentTenantTemplate().insert(objectToSave);
    }
    
    @Override
    public <T> T insert(T objectToSave, String collectionName) {
        return getCurrentTenantTemplate().insert(objectToSave, collectionName);
    }
    
    @Override
    public <T> Collection<T> insert(Collection<? extends T> batchToSave, Class<?> entityClass) {
        return getCurrentTenantTemplate().insert(batchToSave, entityClass);
    }
    
    @Override
    public <T> Collection<T> insert(Collection<? extends T> batchToSave, String collectionName) {
        return getCurrentTenantTemplate().insert(batchToSave, collectionName);
    }
    
    @Override
    public void remove(Object object) {
        getCurrentTenantTemplate().remove(object);
    }
    
    @Override
    public void remove(Object object, String collectionName) {
        getCurrentTenantTemplate().remove(object, collectionName);
    }
    
    @Override
    public long count(Query query, Class<?> entityClass) {
        return getCurrentTenantTemplate().count(query, entityClass);
    }
    
    @Override
    public long count(Query query, String collectionName) {
        return getCurrentTenantTemplate().count(query, collectionName);
    }
    
    @Override
    public <T> T findAndModify(Query query, Update update, Class<T> entityClass) {
        return getCurrentTenantTemplate().findAndModify(query, update, entityClass);
    }
    
    @Override
    public <T> T findAndModify(Query query, Update update, FindAndModifyOptions options, Class<T> entityClass) {
        return getCurrentTenantTemplate().findAndModify(query, update, options, entityClass);
    }
    
    @Override
    public <T> T findAndModify(Query query, Update update, FindAndModifyOptions options, Class<T> entityClass, String collectionName) {
        return getCurrentTenantTemplate().findAndModify(query, update, options, entityClass, collectionName);
    }
}
