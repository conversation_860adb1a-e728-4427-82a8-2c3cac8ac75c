package lk.sout.general.tenant.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lk.sout.general.tenant.context.TenantContext;
import lk.sout.general.tenant.resolver.TenantResolver;
import lk.sout.general.tenant.service.TenantService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * Interceptor to set tenant context for each request
 */
@Component
public class TenantInterceptor implements HandlerInterceptor {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(TenantInterceptor.class);
    
    @Autowired
    private TenantResolver tenantResolver;
    
    @Autowired
    private TenantService tenantService;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            // Skip tenant resolution for certain paths
            if (shouldSkipTenantResolution(request)) {
                TenantContext.setCurrentTenant("default");
                return true;
            }
            
            // Resolve tenant from request
            String tenantId = tenantResolver.resolveTenant(request);
            
            // Validate tenant exists and is active
            if (!tenantService.isValidTenant(tenantId)) {
                LOGGER.warn("Invalid or inactive tenant: {}", tenantId);
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"Invalid tenant\"}");
                return false;
            }
            
            // Set tenant context
            TenantContext.setCurrentTenant(tenantId);
            LOGGER.debug("Tenant context set to: {}", tenantId);
            
            return true;
            
        } catch (Exception e) {
            LOGGER.error("Error in tenant interceptor", e);
            TenantContext.setCurrentTenant("default");
            return true; // Continue with default tenant
        }
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // Clear tenant context after request completion
        TenantContext.clear();
    }
    
    /**
     * Check if tenant resolution should be skipped for this request
     */
    private boolean shouldSkipTenantResolution(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        
        // Skip for login, health checks, actuator endpoints, etc.
        return requestURI.startsWith("/login") ||
               requestURI.startsWith("/actuator") ||
               requestURI.startsWith("/health") ||
               requestURI.startsWith("/error") ||
               requestURI.startsWith("/favicon.ico") ||
               requestURI.startsWith("/static") ||
               requestURI.startsWith("/public");
    }
}
