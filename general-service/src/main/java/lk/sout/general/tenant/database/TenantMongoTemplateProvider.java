package lk.sout.general.tenant.database;

import lk.sout.general.tenant.context.TenantContext;
import lk.sout.general.tenant.entity.Tenant;
import lk.sout.general.tenant.service.TenantService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * Provides MongoTemplate for current tenant context
 */
@Component
public class TenantMongoTemplateProvider {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(TenantMongoTemplateProvider.class);
    
    @Autowired
    private TenantDatabaseManager databaseManager;
    
    @Autowired
    private TenantService tenantService;
    
    @Autowired
    private MongoTemplate defaultMongoTemplate; // For tenant management and default operations
    
    /**
     * Get MongoTemplate for current tenant
     */
    public MongoTemplate getCurrentTenantTemplate() {
        String tenantId = TenantContext.getCurrentTenant();
        
        // Use default template for default tenant or tenant management operations
        if ("default".equals(tenantId)) {
            return defaultMongoTemplate;
        }
        
        try {
            // Get tenant configuration
            Optional<Tenant> tenantOpt = tenantService.getTenantById(tenantId);
            if (tenantOpt.isEmpty()) {
                LOGGER.warn("Tenant not found: {}, using default template", tenantId);
                return defaultMongoTemplate;
            }
            
            Tenant tenant = tenantOpt.get();
            
            // Validate tenant database configuration
            if (!isValidDatabaseConfig(tenant)) {
                LOGGER.warn("Invalid database configuration for tenant: {}, using default template", tenantId);
                return defaultMongoTemplate;
            }
            
            // Get or create MongoTemplate for tenant
            return databaseManager.getMongoTemplate(tenantId, tenant);
            
        } catch (Exception e) {
            LOGGER.error("Error getting MongoTemplate for tenant: {}, using default template", tenantId, e);
            return defaultMongoTemplate;
        }
    }
    
    /**
     * Get MongoTemplate for specific tenant
     */
    public MongoTemplate getTenantTemplate(String tenantId) {
        if ("default".equals(tenantId)) {
            return defaultMongoTemplate;
        }
        
        try {
            Optional<Tenant> tenantOpt = tenantService.getTenantById(tenantId);
            if (tenantOpt.isEmpty()) {
                throw new IllegalArgumentException("Tenant not found: " + tenantId);
            }
            
            Tenant tenant = tenantOpt.get();
            
            if (!isValidDatabaseConfig(tenant)) {
                throw new IllegalArgumentException("Invalid database configuration for tenant: " + tenantId);
            }
            
            return databaseManager.getMongoTemplate(tenantId, tenant);
            
        } catch (Exception e) {
            LOGGER.error("Error getting MongoTemplate for tenant: {}", tenantId, e);
            throw e;
        }
    }
    
    /**
     * Get default MongoTemplate (for tenant management)
     */
    public MongoTemplate getDefaultTemplate() {
        return defaultMongoTemplate;
    }
    
    /**
     * Test database connection for tenant
     */
    public boolean testTenantConnection(String tenantId) {
        try {
            Optional<Tenant> tenantOpt = tenantService.getTenantById(tenantId);
            if (tenantOpt.isEmpty()) {
                return false;
            }
            
            return databaseManager.testConnection(tenantOpt.get());
            
        } catch (Exception e) {
            LOGGER.error("Error testing connection for tenant: {}", tenantId, e);
            return false;
        }
    }
    
    /**
     * Refresh tenant database connection (remove from cache)
     */
    public void refreshTenantConnection(String tenantId) {
        try {
            databaseManager.removeTenant(tenantId);
            LOGGER.info("Refreshed database connection for tenant: {}", tenantId);
        } catch (Exception e) {
            LOGGER.error("Error refreshing connection for tenant: {}", tenantId, e);
        }
    }
    
    /**
     * Validate tenant database configuration
     */
    private boolean isValidDatabaseConfig(Tenant tenant) {
        // Check if database name is provided
        if (tenant.getDatabaseName() == null || tenant.getDatabaseName().trim().isEmpty()) {
            return false;
        }
        
        // If connection string is provided, it should be valid
        if (tenant.getConnectionString() != null && !tenant.getConnectionString().trim().isEmpty()) {
            return tenant.getConnectionString().startsWith("mongodb://") || 
                   tenant.getConnectionString().startsWith("mongodb+srv://");
        }
        
        // If no connection string, check individual components
        // Host is optional (defaults to localhost)
        // Port is optional (defaults to 27017)
        // Username and password are optional
        
        return true; // Basic validation passed
    }
}
