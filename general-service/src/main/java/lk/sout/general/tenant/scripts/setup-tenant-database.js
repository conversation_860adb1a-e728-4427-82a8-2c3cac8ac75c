// MongoDB script to setup a new tenant database
// Usage: mongo setup-tenant-database.js

// Configuration
const TENANT_ID = "tenant1";
const DATABASE_NAME = "tenant1_db";
const USERNAME = "tenant1_user";
const PASSWORD = "secure_password";

print("Setting up database for tenant: " + TENANT_ID);

// Switch to the tenant database
use(DATABASE_NAME);

// Create user for the tenant database
db.createUser({
  user: USERNAME,
  pwd: PASSWORD,
  roles: [
    {
      role: "readWrite",
      db: DATABASE_NAME
    }
  ]
});

print("Created user: " + USERNAME + " for database: " + DATABASE_NAME);

// Create initial collections with indexes
print("Creating collections and indexes...");

// Users collection
db.createCollection("user");
db.user.createIndex({ "username": 1 }, { unique: true });
db.user.createIndex({ "email": 1 }, { unique: true });

// Items collection
db.createCollection("item");
db.item.createIndex({ "itemCode": 1 }, { unique: true });
db.item.createIndex({ "barcode": 1 }, { unique: true });
db.item.createIndex({ "itemName": 1 });
db.item.createIndex({ "active": 1 });

// Stock collection
db.createCollection("stock");
db.stock.createIndex({ "itemCode": 1, "warehouseCode": 1, "sellingPrice": 1 }, { unique: true });

// Serial Numbers collection
db.createCollection("serialNumber");
db.serialNumber.createIndex({ "serialNumber": 1 }, { unique: true });
db.serialNumber.createIndex({ "itemCode": 1 });
db.serialNumber.createIndex({ "status": 1 });

// Sales Invoice collection
db.createCollection("salesInvoice");
db.salesInvoice.createIndex({ "invoiceNumber": 1 }, { unique: true });
db.salesInvoice.createIndex({ "customerId": 1 });
db.salesInvoice.createIndex({ "invoiceDate": 1 });

// Purchase Invoice collection
db.createCollection("purchaseInvoice");
db.purchaseInvoice.createIndex({ "invoiceNumber": 1 }, { unique: true });
db.purchaseInvoice.createIndex({ "supplierId": 1 });
db.purchaseInvoice.createIndex({ "invoiceDate": 1 });

// Customers collection
db.createCollection("customer");
db.customer.createIndex({ "customerNumber": 1 }, { unique: true });
db.customer.createIndex({ "email": 1 });

// Suppliers collection
db.createCollection("supplier");
db.supplier.createIndex({ "supplierCode": 1 }, { unique: true });
db.supplier.createIndex({ "email": 1 });

// Warehouses collection
db.createCollection("warehouse");
db.warehouse.createIndex({ "code": 1 }, { unique: true });

// Categories collection
db.createCollection("itemCategory");
db.itemCategory.createIndex({ "name": 1 }, { unique: true });

// Brands collection
db.createCollection("brand");
db.brand.createIndex({ "name": 1 }, { unique: true });

// Models collection
db.createCollection("model");
db.model.createIndex({ "name": 1 }, { unique: true });

// Departments collection
db.createCollection("department");
db.department.createIndex({ "name": 1 }, { unique: true });

// Settings collection
db.createCollection("generalSettings");

print("Database setup completed for tenant: " + TENANT_ID);
print("Database: " + DATABASE_NAME);
print("User: " + USERNAME);
print("Collections and indexes created successfully");

// Test the connection
print("Testing database connection...");
try {
    db.runCommand({ ping: 1 });
    print("✓ Database connection test successful");
} catch (e) {
    print("✗ Database connection test failed: " + e);
}

print("Setup complete!");
