package lk.sout.general.tenant.entity;

/**
 * Marker interface for tenant-aware entities
 * Since we use separate databases per tenant, no tenant ID field is needed
 */
public abstract class TenantAware {

    /**
     * This is a marker class to identify entities that should use tenant-specific databases
     * No additional fields or logic needed since database isolation handles tenant separation
     */

}
