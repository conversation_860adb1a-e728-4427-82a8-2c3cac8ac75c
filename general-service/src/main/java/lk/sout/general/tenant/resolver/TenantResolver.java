package lk.sout.general.tenant.resolver;

import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Resolves tenant ID from HTTP request
 */
@Component
public class TenantResolver {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(TenantResolver.class);
    
    // Header name for tenant ID
    private static final String TENANT_HEADER = "X-Tenant-ID";
    
    // Subdomain extraction
    private static final String SUBDOMAIN_SEPARATOR = ".";
    
    /**
     * Resolve tenant ID from HTTP request
     * Priority: Header > Subdomain > Default
     */
    public String resolveTenant(HttpServletRequest request) {
        String tenantId = null;
        
        // 1. Try to get tenant from header
        tenantId = getTenantFromHeader(request);
        if (tenantId != null) {
            LOGGER.debug("Tenant resolved from header: {}", tenantId);
            return tenantId;
        }
        
        // 2. Try to get tenant from subdomain
        tenantId = getTenantFromSubdomain(request);
        if (tenantId != null) {
            LOGGER.debug("Tenant resolved from subdomain: {}", tenantId);
            return tenantId;
        }
        
        // 3. Try to get tenant from path parameter
        tenantId = getTenantFromPath(request);
        if (tenantId != null) {
            LOGGER.debug("Tenant resolved from path: {}", tenantId);
            return tenantId;
        }
        
        // 4. Default tenant
        LOGGER.debug("Using default tenant");
        return "default";
    }
    
    /**
     * Get tenant ID from request header
     */
    private String getTenantFromHeader(HttpServletRequest request) {
        String tenantId = request.getHeader(TENANT_HEADER);
        return isValidTenantId(tenantId) ? tenantId : null;
    }
    
    /**
     * Get tenant ID from subdomain
     * Example: demo.vaganana.com -> demo, wanigarathna.vaganana.com -> wanigarathna
     */
    private String getTenantFromSubdomain(HttpServletRequest request) {
        String serverName = request.getServerName();
        if (serverName == null) {
            return null;
        }

        // Check if it's a vaganana.com domain
        if (serverName.endsWith(".vaganana.com")) {
            String[] parts = serverName.split("\\" + SUBDOMAIN_SEPARATOR);
            if (parts.length >= 3) { // subdomain.vaganana.com
                String subdomain = parts[0];
                // Exclude common subdomains
                if (!isCommonSubdomain(subdomain)) {
                    return isValidTenantId(subdomain) ? subdomain : null;
                }
            }
        }

        return null;
    }
    
    /**
     * Get tenant ID from request path
     * Example: /api/tenant1/users -> tenant1
     */
    private String getTenantFromPath(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        if (requestURI == null) {
            return null;
        }
        
        // Pattern: /api/{tenantId}/...
        String[] pathParts = requestURI.split("/");
        if (pathParts.length >= 3 && "api".equals(pathParts[1])) {
            String tenantId = pathParts[2];
            return isValidTenantId(tenantId) ? tenantId : null;
        }
        
        return null;
    }
    
    /**
     * Check if subdomain is a common one (www, api, admin, etc.)
     */
    private boolean isCommonSubdomain(String subdomain) {
        return subdomain.equals("www") || 
               subdomain.equals("api") || 
               subdomain.equals("admin") || 
               subdomain.equals("app") ||
               subdomain.equals("portal");
    }
    
    /**
     * Validate tenant ID format
     */
    private boolean isValidTenantId(String tenantId) {
        if (tenantId == null || tenantId.trim().isEmpty()) {
            return false;
        }
        
        // Basic validation: alphanumeric and hyphens, 3-50 characters
        return tenantId.matches("^[a-zA-Z0-9-]{3,50}$");
    }
}
