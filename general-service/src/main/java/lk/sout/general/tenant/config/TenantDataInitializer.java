package lk.sout.general.tenant.config;

import lk.sout.general.tenant.entity.Tenant;
import lk.sout.general.tenant.service.TenantService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * Initialize tenant data for existing deployments
 */
@Component
public class TenantDataInitializer implements CommandLineRunner {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(TenantDataInitializer.class);
    
    @Autowired
    private TenantService tenantService;
    
    @Value("${mongodb.host:localhost}")
    private String mongoHost;

    @Value("${mongodb.port:27017}")
    private int mongoPort;

    @Value("${mongodb.username:generalWeb}")
    private String mongoUsername;

    @Value("${mongodb.password:}")
    private String mongoPassword;
    
    @Override
    public void run(String... args) throws Exception {
        LOGGER.info("Initializing tenant data...");
        
        // Initialize demo tenant
        initializeDemoTenant();
        
        // Initialize wanigarathna tenant
        initializeWanigarathnaTenant();
        
        LOGGER.info("Tenant data initialization completed");
    }
    
    private void initializeDemoTenant() {
        String tenantId = "demo";
        
        try {
            Optional<Tenant> existingTenant = tenantService.getTenantById(tenantId);
            if (existingTenant.isPresent()) {
                LOGGER.info("Demo tenant already exists, skipping initialization");
                return;
            }
            
            Tenant demoTenant = new Tenant();
            demoTenant.setTenantId(tenantId);
            demoTenant.setSubdomain("demo");
            demoTenant.setName("Demo Tenant");
            demoTenant.setDescription("Demo environment for testing");
            demoTenant.setContactEmail("<EMAIL>");
            demoTenant.setActive(true);
            demoTenant.setMaxUsers(100);
            
            // Database configuration
            demoTenant.setDatabaseName("generalWebDemo");
            demoTenant.setDatabaseHost(mongoHost);
            demoTenant.setDatabasePort(mongoPort);
            demoTenant.setDatabaseUsername(mongoUsername);
            demoTenant.setDatabasePassword(mongoPassword);

            // Build connection string with authentication
            String connectionString = String.format("mongodb://%s:%s@%s:%d/generalWebDemo",
                mongoUsername, mongoPassword, mongoHost, mongoPort);
            demoTenant.setConnectionString(connectionString);
            
            tenantService.createTenant(demoTenant);
            LOGGER.info("Created demo tenant with database: generalWebDemo");
            
        } catch (Exception e) {
            LOGGER.error("Error initializing demo tenant", e);
        }
    }
    
    private void initializeWanigarathnaTenant() {
        String tenantId = "wanigarathna";
        
        try {
            Optional<Tenant> existingTenant = tenantService.getTenantById(tenantId);
            if (existingTenant.isPresent()) {
                LOGGER.info("Wanigarathna tenant already exists, skipping initialization");
                return;
            }
            
            Tenant wanigarathnaTenant = new Tenant();
            wanigarathnaTenant.setTenantId(tenantId);
            wanigarathnaTenant.setSubdomain("wanigarathna");
            wanigarathnaTenant.setName("Wanigarathna Tenant");
            wanigarathnaTenant.setDescription("Wanigarathna production environment");
            wanigarathnaTenant.setContactEmail("<EMAIL>");
            wanigarathnaTenant.setActive(true);
            wanigarathnaTenant.setMaxUsers(50);
            
            // Database configuration
            wanigarathnaTenant.setDatabaseName("generalWebWanigarathna");
            wanigarathnaTenant.setDatabaseHost(mongoHost);
            wanigarathnaTenant.setDatabasePort(mongoPort);
            wanigarathnaTenant.setDatabaseUsername(mongoUsername);
            wanigarathnaTenant.setDatabasePassword(mongoPassword);

            // Build connection string with authentication
            String connectionString = String.format("mongodb://%s:%s@%s:%d/generalWebWanigarathna",
                mongoUsername, mongoPassword, mongoHost, mongoPort);
            wanigarathnaTenant.setConnectionString(connectionString);
            
            tenantService.createTenant(wanigarathnaTenant);
            LOGGER.info("Created wanigarathna tenant with database: generalWebWanigarathna");
            
        } catch (Exception e) {
            LOGGER.error("Error initializing wanigarathna tenant", e);
        }
    }
}
