spring.application.name=General

#sout.customer=
#sout.customer=Demo
#sout.customer=Brilliant
#sout.customer=BrilliantTools
#sout.customer=SmartLife
#sout.customer=SmartLife2
#sout.customer=Union
#sout.customer=Chathuranga
#sout.customer=RehansaTan
#sout.customer=RehansaHamd
#sout.customer=AjStore
#sout.customer=NewCityMobile
sout.customer=Wanigarathna


# MongoDB - Default database (used by DbConfig for system/shared data)
spring.data.mongodb.host=localhost
#spring.data.mongodb.host=*************
spring.data.mongodb.port=27017
spring.data.mongodb.database=generalWeb
spring.data.mongodb.authDatabase=generalWeb
spring.data.mongodb.username=generalWeb
spring.data.mongodb.password=awer@#$cdfDDF!@S_+(

# MongoDB configuration for tenant-specific databases (used by TenantDatabaseService)
mongodb.host=localhost
mongodb.port=27017
mongodb.username=generalWeb
mongodb.password=awer@#$cdfDDF!@S_+(

# Tenant Configuration
tenant.database.prefix=generalWeb
tenant.default.domain=vaganana.com
tenant.default.maxUsers=50
tenant.initialize.list=demo,wanigarathna
tenant.auto.create.enabled=true
tenant.auto.create.onFirstAccess=true

# MULTIPART (MultipartProperties)
# Enable multipart uploads
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.max-file-size=200MB
spring.servlet.multipart.max-request-size=215MB

spring.main.allow-circular-references=true

sout.mainWhCode=0

#File logging
#logging.level.root=info
#logging.pattern.console=%d{dd-MM-yyyy HH:mm:ss.SSS} %magenta([%thread]) %highlight(%-5level) %logger.%M - %msg%n
#logging.file.path=/var/log/tomcat11/sout.${sout.customer}.log
#logging.pattern.file=%d{dd-MM-yyyy HH:mm:ss.SSS} :${sout.customer} [%thread] %-5level %logger{36}.%M - %msg%n
#logging.logback.rollingpolicy.max-file-size=10MB

dateFormat=MM/dd/yyyy
dateTimeFormat=MM/dd/yyyy HH:mm:ss

#spring.boot.admin.client.username=madhawa
#spring.boot.admin.client.password=madhawa
#spring.boot.admin.client.enabled=true
#spring.boot.admin.client.auto-registration=true
#spring.boot.admin.client.instance.service-base-url=http://************
#spring.boot.admin.client.url=http://************:8080
#management.endpoints.web.exposure.include=*
#management.endpoint.health.show-details=always
