# Tenant Configuration Properties
# This file contains configuration for flexible tenant initialization

# Database Configuration
tenant.database.prefix=generalWeb
tenant.default.domain=vaganana.com
tenant.default.maxUsers=50

# Tenants to Initialize on Startup
# Comma-separated list of tenant subdomains
tenant.initialize.list=demo,wanigarathna

# Custom Tenant Configurations
# You can override default settings for specific tenants

# Demo Tenant Settings
tenant.demo.name=Demo Tenant
tenant.demo.description=Demo environment for testing
tenant.demo.maxUsers=100
tenant.demo.contactEmail=<EMAIL>

# Wanigarathna Tenant Settings  
tenant.wanigarathna.name=Wanigarathna Tenant
tenant.wanigarathna.description=Wanigarathna production environment
tenant.wanigarathna.maxUsers=50
tenant.wanigarathna.contactEmail=<EMAIL>

# Add more tenants as needed:
# tenant.newclient.name=New Client Tenant
# tenant.newclient.description=New client environment
# tenant.newclient.maxUsers=25
# tenant.newclient.contactEmail=<EMAIL>

# Auto-creation Settings
tenant.auto.create.enabled=true
tenant.auto.create.onFirstAccess=true
