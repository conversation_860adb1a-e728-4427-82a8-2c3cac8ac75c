import {Item} from '../../inventory/model/item';
import {MetaData} from '../../../core/model/metaData';
import {Stock} from '../../inventory/model/stock';

export class SalesInvoiceRecord {

  id: string;

  itemCode: string;

  barcode: string;

  itemName: string;

  item: Item;

  quantity: number;

  unitPrice: number;

  // what ever the chane we do to the unit price this will remain unchanged as original selling price.This and itemcode both use to find the stock record`
  unitPriceOriginal: number;

  itemCost: number;

  discount: number;

  price: number;

  date: string;

  drawerNo: string;

  routeNo: string;

  routeName: string;

  recordType: string;

  paymentStatus: string;

  stock: Stock;
}
